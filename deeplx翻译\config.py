# -*- coding: utf-8 -*-
"""
DeepL翻译配置文件
"""

# API配置
API = {
    'url': 'https://api.deeplx.org/hoXgMRYL9sVFG5tKmkxF663lHQlO4F76TeZKJUlCG94/translate'
}

# 支持的语言
LANGUAGES = {
    'auto': '自动检测',
    'ZH': '中文',
    'EN': '英语',
    'JA': '日语'
}

# UI配置
UI = {
    'title': '翻译助手',
    'width': 900,
    'height': 600,
    'font': ('微软雅黑', 10),          # 基础字体
    'text_font': ('微软雅黑', 11),     # 文本区域字体
    'button_font': ('微软雅黑', 10),   # 按钮字体
    'padding': 10,
    
    # 颜色配置
    'bg': '#f5f5f5',           # 背景色
    'fg': '#333333',           # 前景色
    'button_bg': '#e1e1e1',    # 按钮背景色（改为浅灰色）
    'button_fg': '#333333',    # 按钮文字颜色（改为深灰色）
    'text_bg': '#ffffff',      # 文本区背景
    'text_fg': '#333333',      # 文本区文字颜色
    'border': '#e8e8e8',       # 边框颜色
    'highlight_bg': '#e6f4ff', # 高亮背景
    'highlight_fg': '#333333', # 高亮文字
    
    # 组件特定配置
    'text': {
        'height': 12,
        'relief': 'solid',
        'borderwidth': 1,
        'padx': 8,
        'pady': 8
    },
    'button': {
        'width': 15,
        'height': 1,
        'padding': 5
    },
    'combo': {
        'width': 12,
        'relief': 'solid',
        'borderwidth': 1
    },
    'swap_button': {
        'width': 2,
        'relief': 'flat',
        'borderwidth': 0,
        'padding': 5,
        'text': '⇌'
    }
}

# 应用配置
APP = {
    'version': '1.0.0',
    'author': 'chengbeyond',
    'description': 'DeepL翻译助手 - 支持多语言在线翻译',
    'state_save_interval': 5000,  # 状态保存间隔（毫秒）
    'max_history_records': 100  # 最大历史记录数
}

# 文件路径
FILES = {
    'history': 'translation_history.json',
    'window_state': 'window_state.json'
}
