import requests
from bs4 import <PERSON><PERSON><PERSON>p
import logging
import os
from concurrent.futures import Thread<PERSON>oolExecutor
from requests.adapters import HTT<PERSON><PERSON>pter
from requests.packages.urllib3.util.retry import Retry


def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)


def create_session():
    """创建带有重试机制的请求会话"""
    session = requests.Session()
    retry = Retry(total=3, backoff_factor=0.5, status_forcelist=[500, 502, 503, 504])
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    return session


def fetch_ip_data(url, logger):
    """获取网页数据"""
    session = create_session()
    try:
        logger.info(f"正在请求: {url}")
        response = session.get(url, timeout=10)
        response.raise_for_status()
        return response.content
    except requests.exceptions.RequestException as e:
        logger.error(f"请求失败: {e}")
        return None


def parse_ip_data(html_content, logger):
    """解析HTML内容提取IP数据"""
    if not html_content:
        return []

    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        centered_div = soup.find('div', class_='centered')
        if not centered_div:
            logger.error("未找到class='centered'的div")
            return []

        table = centered_div.find('table')
        if not table:
            logger.error("未找到表格")
            return []

        results = []
        rows = table.find_all('tr')[:11]  # 只获取前10行

        for row in rows:
            cells = row.find_all('td')
            if len(cells) >= 5:
                results.append({
                    'service_provider': cells[0].text.strip(),
                    'ip_address': cells[1].text.strip(),
                    'latency': cells[2].text.strip(),
                    'packet_loss': cells[3].text.strip(),
                    'speed': cells[4].text.strip()
                })

        return results
    except Exception as e:
        logger.error(f"解析数据失败: {e}")
        return []


def save_results(results, output_file, logger):
    """保存结果到文件"""
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        with open(output_file, 'w', encoding='utf-8') as file:
            for item in results:
                formatted_output = (
                    f"服务商：{item['service_provider']}，"
                    f"IP：{item['ip_address']}，"
                    f"延迟：{item['latency']}，"
                    f"丢包率：{item['packet_loss']}，"
                    f"速度：{item['speed']}\n"
                )
                file.write(formatted_output)

        logger.info(f"结果已保存到: {output_file}")
        return True
    except Exception as e:
        logger.error(f"保存结果失败: {e}")
        return False


def main():
    """主函数"""
    logger = setup_logging()
    url = 'https://cf.090227.xyz/'
    # 使用脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_file = os.path.join(script_dir, 'results.txt')

    logger.info("开始获取CF优选IP数据")
    html_content = fetch_ip_data(url, logger)

    if html_content:
        results = parse_ip_data(html_content, logger)
        if results:
            logger.info(f"成功获取到 {len(results)} 条IP数据")
            save_results(results, output_file, logger)
        else:
            logger.warning("没有获取到IP数据")
    else:
        logger.error("获取网页内容失败")


if __name__ == "__main__":
    main()
