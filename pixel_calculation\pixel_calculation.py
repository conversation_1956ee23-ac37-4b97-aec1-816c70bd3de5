#! /usr/bin/env python
#  -*- coding: utf-8 -*-
#
# GUI module generated by PAGE version 4.19
#  in conjunction with Tcl version 8.6
#    Jul 11, 2024 06:17:26 PM CST  platform: Windows NT

import sys

try:
    import Tkinter as tk
except ImportError:
    import tkinter as tk

try:
    import ttk

    py3 = False
except ImportError:
    import tkinter.ttk as ttk

    py3 = True

import pixel_calculation_support


def vp_start_gui():
    '''Starting point when module is the main routine.'''
    global val, w, root
    root = tk.Tk()
    root.resizable(False, False)
    root.update()
    screenwidth = root.winfo_screenwidth()
    screenheight = root.winfo_screenheight()
    width = 442
    height = 107
    size = '%dx%d+%d+%d' % (width, height, (screenwidth - width) / 2, (screenheight - height) / 2)
    root.geometry(size)

    top = Toplevel1(root)
    pixel_calculation_support.init(root, top)
    root.mainloop()


w = None


def create_Toplevel1(root, *args, **kwargs):
    '''Starting point when module is imported by another program.'''
    global w, w_win, rt
    rt = root
    w = tk.Toplevel(root)
    top = Toplevel1(w)
    pixel_calculation_support.init(w, top, *args, **kwargs)
    return (w, top)


def destroy_Toplevel1():
    global w
    w.destroy()
    w = None


class Toplevel1:
    def __init__(self, top=None):
        '''This class configures and populates the toplevel window.'''
        _bgcolor = '#d9d9d9'  # X11 color: 'gray85'
        _fgcolor = '#000000'  # X11 color: 'black'
        _compcolor = '#d9d9d9'  # X11 color: 'gray85'
        _ana1color = '#d9d9d9'  # X11 color: 'gray85'
        _ana2color = '#ececec'  # Closest X11 color: 'gray92'

        top.title("Pixel Calculation")
        top.configure(background=_bgcolor)

        self.TEntry1 = ttk.Entry(top)
        self.TEntry1.place(relx=0.136, rely=0.187, relheight=0.215, relwidth=0.33)
        self.TEntry1.configure(cursor="ibeam")

        self.TEntry2 = ttk.Entry(top)
        self.TEntry2.place(relx=0.611, rely=0.187, relheight=0.215, relwidth=0.33)
        self.TEntry2.configure(cursor="ibeam")

        self.TLabel1 = ttk.Label(top)
        self.TLabel1.place(relx=0.023, rely=0.187, height=21, width=40)
        self.TLabel1.configure(background="#d9d9d9")
        self.TLabel1.configure(foreground="#000000")
        self.TLabel1.configure(font="TkDefaultFont")
        self.TLabel1.configure(relief='flat')
        self.TLabel1.configure(text='''原始宽''')

        self.TLabel2 = ttk.Label(top)
        self.TLabel2.place(relx=0.498, rely=0.187, height=21, width=40)
        self.TLabel2.configure(background="#d9d9d9")
        self.TLabel2.configure(foreground="#000000")
        self.TLabel2.configure(font="TkDefaultFont")
        self.TLabel2.configure(relief='flat')
        self.TLabel2.configure(text='''原始高''')

        self.TLabel3 = ttk.Label(top)
        self.TLabel3.place(relx=0.023, rely=0.561, height=21, width=40)
        self.TLabel3.configure(background="#d9d9d9")
        self.TLabel3.configure(foreground="#000000")
        self.TLabel3.configure(font="TkDefaultFont")
        self.TLabel3.configure(relief='flat')
        self.TLabel3.configure(text='''修改宽''')

        self.TLabel4 = ttk.Label(top)
        self.TLabel4.place(relx=0.498, rely=0.561, height=21, width=40)
        self.TLabel4.configure(background="#d9d9d9")
        self.TLabel4.configure(foreground="#000000")
        self.TLabel4.configure(font="TkDefaultFont")
        self.TLabel4.configure(relief='flat')
        self.TLabel4.configure(text='''修改高''')

        self.TEntry3 = ttk.Entry(top)
        self.TEntry3.place(relx=0.136, rely=0.561, relheight=0.215, relwidth=0.33)
        self.TEntry3.configure(cursor="ibeam")
        self.TEntry3.bind('<KeyRelease>', lambda event: self.update_dimensions(event, 'width'))

        self.TEntry4 = ttk.Entry(top)
        self.TEntry4.place(relx=0.611, rely=0.561, relheight=0.215, relwidth=0.33)
        self.TEntry4.configure(cursor="ibeam")
        self.TEntry4.bind('<KeyRelease>', lambda event: self.update_dimensions(event, 'height'))

    def update_dimensions(self, event, dimension):
        try:
            orig_width = float(self.TEntry1.get()) if self.TEntry1.get() else 0
            orig_height = float(self.TEntry2.get()) if self.TEntry2.get() else 0
            new_width = float(self.TEntry3.get()) if dimension == 'width' and self.TEntry3.get() else None
            new_height = float(self.TEntry4.get()) if dimension == 'height' and self.TEntry4.get() else None

            if dimension == 'width':
                if new_width and orig_height:
                    ratio = orig_height / orig_width
                    self.TEntry4.delete(0, tk.END)
                    self.TEntry4.insert(0, str(round(new_width * ratio)))
            elif dimension == 'height':
                if new_height and orig_width:
                    ratio = orig_width / orig_height
                    self.TEntry3.delete(0, tk.END)
                    self.TEntry3.insert(0, str(round(new_height * ratio)))
        except ValueError:
            pass  # Ignore invalid input


if __name__ == '__main__':
    vp_start_gui()
