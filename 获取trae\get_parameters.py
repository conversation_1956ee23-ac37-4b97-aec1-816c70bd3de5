import json
import os

def get_trae_token():
    """
    从storage.json文件中获取token值
    """
    try:
        # 使用指定的路径
        storage_path = os.path.join(os.getenv('APPDATA'), 'Trae', 'User', 'globalStorage', 'storage.json')
        
        if not os.path.exists(storage_path):
            return None
            
        with open(storage_path, 'r', encoding='utf-8') as f:
            data = json.loads(f.read())
            
        icube_info = data.get("iCubeAuthInfo://icube.cloudide")
        if not icube_info:
            return None
            
        if isinstance(icube_info, str):
            try:
                icube_data = json.loads(icube_info)
                if isinstance(icube_data, dict):
                    token = icube_data.get("token")
                    if token:
                        print(token)
                        return token
            except json.JSONDecodeError:
                return None
        return None
            
    except Exception:
        return None

if __name__ == "__main__":
    get_trae_token()
