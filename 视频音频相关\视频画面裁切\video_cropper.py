import sys
import cv2
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, 
                           QVBoxLayout, QWidget, QLabel, QFileDialog, QProgressDialog)
from PyQt5.QtGui import QImage, QPixmap, QPainter, QPen
from PyQt5.QtCore import Qt, QTimer, QRect, QPoint, QSize, QSettings
import os
import tempfile
import subprocess
import platform

# 常量定义区
CROP_HANDLE_RADIUS = 8  # 拖拽点半径，适当减小更美观
CROP_HANDLE_HIT_RADIUS = 20  # 手柄判定半径，提升易用性
TIMER_INTERVAL_MS = 33   # 定时器间隔（毫秒）
MIN_CROP_SIZE = 20       # 最小裁剪框尺寸

class VideoLabel(QLabel):
    """自定义视频显示控件，支持裁剪框绘制与交互（限定在display_rect内）"""
    def __init__(self):
        super().__init__()
        self.crop_rect = None  # 注意：此rect为display_rect内的相对坐标
        self.drag_start = None
        self.dragging = False
        self.dragging_edge = None
        self.display_rect = QRect(0, 0, 0, 0)
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMinimumSize(640, 480)
        self.setStyleSheet("background-color: black;")
        self.setAutoFillBackground(True)

    def set_display_rect(self, rect):
        self.display_rect = rect

    def limit_crop_rect(self):
        # 限制裁剪框在display_rect内（以display_rect为原点）
        if not self.crop_rect or not self.display_rect.isValid():
            return
        r = self.crop_rect
        d = self.display_rect
        x = max(0, min(r.left(), d.width() - MIN_CROP_SIZE))
        y = max(0, min(r.top(), d.height() - MIN_CROP_SIZE))
        w = max(MIN_CROP_SIZE, min(r.width(), d.width() - x))
        h = max(MIN_CROP_SIZE, min(r.height(), d.height() - y))
        self.crop_rect = QRect(x, y, w, h)

    def mousePressEvent(self, event):
        if not self.display_rect.isValid():
            return super().mousePressEvent(event)
        pos = event.pos()
        # 只响应display_rect区域内的鼠标事件
        if not self.display_rect.contains(pos):
            return super().mousePressEvent(event)
        local_pos = pos - self.display_rect.topLeft()  # 转为display_rect内坐标
        if event.button() == Qt.LeftButton and self.crop_rect:
            # 检查是否点击在调整大小的点上
            points = [
                self.crop_rect.topLeft(),
                self.crop_rect.topRight(),
                self.crop_rect.bottomLeft(),
                self.crop_rect.bottomRight()
            ]
            for i, point in enumerate(points):
                if (point - local_pos).manhattanLength() < CROP_HANDLE_HIT_RADIUS:
                    self.dragging_edge = i
                    self.drag_start = local_pos
                    self.dragging = True
                    return
            # 检查是否点击在裁剪框内
            if self.crop_rect.contains(local_pos):
                self.dragging_edge = None
                self.drag_start = local_pos - self.crop_rect.topLeft()
                self.dragging = True
                return
        # 新建裁剪框
        self.dragging = True
        self.dragging_edge = None
        self.drag_start = local_pos
        self.crop_rect = QRect(local_pos, local_pos)
        self.repaint()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if not self.display_rect.isValid():
            return super().mouseMoveEvent(event)
        pos = event.pos()
        local_pos = pos - self.display_rect.topLeft()
        cursor_set = False
        if self.crop_rect:
            # 四个角
            points = [
                self.crop_rect.topLeft(),
                self.crop_rect.topRight(),
                self.crop_rect.bottomLeft(),
                self.crop_rect.bottomRight()
            ]
            # 对应手势
            cursors = [
                Qt.SizeFDiagCursor,  # 左上
                Qt.SizeBDiagCursor,  # 右上
                Qt.SizeBDiagCursor,  # 左下
                Qt.SizeFDiagCursor   # 右下
            ]
            for i, point in enumerate(points):
                if (point - local_pos).manhattanLength() < CROP_HANDLE_HIT_RADIUS:
                    self.setCursor(cursors[i])
                    cursor_set = True
                    break
            if not cursor_set and self.crop_rect.contains(local_pos):
                self.setCursor(Qt.SizeAllCursor)
                cursor_set = True
        if not cursor_set:
            self.setCursor(Qt.ArrowCursor)
        if not self.dragging:
            return super().mouseMoveEvent(event)
        # 只允许在display_rect内拖动
        local_pos.setX(max(0, min(local_pos.x(), self.display_rect.width()-1)))
        local_pos.setY(max(0, min(local_pos.y(), self.display_rect.height()-1)))
        if self.crop_rect:
            if self.dragging_edge is None:
                # 拖动裁剪框
                if self.drag_start is not None:
                    new_top_left = local_pos - self.drag_start
                    # 限制裁剪框不超出display_rect
                    new_top_left.setX(max(0, min(new_top_left.x(), self.display_rect.width() - self.crop_rect.width())))
                    new_top_left.setY(max(0, min(new_top_left.y(), self.display_rect.height() - self.crop_rect.height())))
                    self.crop_rect.moveTopLeft(new_top_left)
            else:
                # 调整裁剪框大小
                if self.dragging_edge == 0:  # 左上角
                    self.crop_rect.setTopLeft(local_pos)
                elif self.dragging_edge == 1:  # 右上角
                    self.crop_rect.setTopRight(local_pos)
                elif self.dragging_edge == 2:  # 左下角
                    self.crop_rect.setBottomLeft(local_pos)
                elif self.dragging_edge == 3:  # 右下角
                    self.crop_rect.setBottomRight(local_pos)
                # 限制宽高不为负
                if self.crop_rect.width() < 0:
                    left = self.crop_rect.left()
                    self.crop_rect.setLeft(self.crop_rect.right())
                    self.crop_rect.setRight(left)
                if self.crop_rect.height() < 0:
                    top = self.crop_rect.top()
                    self.crop_rect.setTop(self.crop_rect.bottom())
                    self.crop_rect.setBottom(top)
            self.limit_crop_rect()
        self.repaint()
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragging = False
            self.dragging_edge = None
        super().mouseReleaseEvent(event)

    def leaveEvent(self, event):
        self.setCursor(Qt.ArrowCursor)
        super().leaveEvent(event)

    def paintEvent(self, event):
        super().paintEvent(event)
        # 画display_rect边框（调试用）
        if self.display_rect.isValid():
            painter = QPainter(self)
            pen = QPen(Qt.green, 1, Qt.DashLine)
            painter.setPen(pen)
            painter.drawRect(self.display_rect)
            painter.end()
        # 画裁剪框（以display_rect为原点）
        if self.crop_rect and self.crop_rect.isValid():
            painter = QPainter(self)
            pen = QPen(Qt.red, 2)
            painter.setPen(pen)
            # 裁剪框实际显示位置 = display_rect左上角 + crop_rect
            draw_rect = QRect(self.display_rect.topLeft() + self.crop_rect.topLeft(), self.crop_rect.size())
            painter.drawRect(draw_rect)
            # 画调整点（圆心正好在四角）
            points = [
                draw_rect.topLeft(),
                draw_rect.topRight(),
                draw_rect.bottomLeft(),
                draw_rect.bottomRight()
            ]
            painter.setBrush(Qt.red)
            for point in points:
                painter.drawEllipse(point, CROP_HANDLE_RADIUS, CROP_HANDLE_RADIUS)
            # 计算原视频分辨率
            main_window = self.window()
            if hasattr(main_window, 'cap') and main_window.cap:
                video_w = int(main_window.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                video_h = int(main_window.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                scale_w = video_w / self.display_rect.width()
                scale_h = video_h / self.display_rect.height()
                crop_w = int(self.crop_rect.width() * scale_w)
                crop_h = int(self.crop_rect.height() * scale_h)
                painter.setPen(Qt.yellow)
                painter.drawText(draw_rect.topLeft() + QPoint(5, 20), f'{crop_w}×{crop_h}')
            painter.end()

    def reset_crop_rect(self):
        # 重置裁剪框为display_rect区域
        if self.display_rect.isValid():
            self.crop_rect = QRect(0, 0, self.display_rect.width(), self.display_rect.height())
        else:
            self.crop_rect = None
        self.repaint()

class VideoCropper(QMainWindow):
    """主窗口，负责视频加载、播放、裁剪与保存"""
    def __init__(self):
        super().__init__()
        self.settings = QSettings('MyCompany', 'VideoCropper')
        self.last_dir = self.settings.value('last_dir', os.getcwd())
        self.video_path = None
        self.cap = None
        self.current_frame = None
        self.is_paused = False
        self.setup_ui()
        self.setup_video_player()

    def setup_ui(self):
        self.setWindowTitle('视频裁剪工具')
        self.setGeometry(100, 100, 1000, 600)

        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 创建视频显示标签
        self.video_label = VideoLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setStyleSheet("background-color: black;")
        layout.addWidget(self.video_label)

        # 创建控制按钮
        button_layout = QVBoxLayout()
        
        load_button = QPushButton('加载视频')
        load_button.clicked.connect(self.load_video)
        button_layout.addWidget(load_button)

        save_button = QPushButton('保存裁剪')
        save_button.clicked.connect(self.save_cropped_video)
        button_layout.addWidget(save_button)

        reset_button = QPushButton('重置裁剪框')
        reset_button.clicked.connect(self.video_label.reset_crop_rect)
        button_layout.addWidget(reset_button)

        pause_button = QPushButton('暂停/播放')
        pause_button.clicked.connect(self.toggle_pause)
        button_layout.addWidget(pause_button)

        layout.addLayout(button_layout)

    def setup_video_player(self):
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        self.timer.start(TIMER_INTERVAL_MS)

    def load_video(self):
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            self.last_dir,
            "视频文件 (*.mp4 *.avi *.mov);;所有文件 (*)",
            options=options
        )
        if file_name:
            self.last_dir = os.path.dirname(file_name)
            self.settings.setValue('last_dir', self.last_dir)
            if self.cap:
                self.cap.release()
            self.video_path = file_name
            self.cap = cv2.VideoCapture(self.video_path)
            self.is_paused = False
            self.timer.start(TIMER_INTERVAL_MS)
            self.video_label.crop_rect = None
            self.video_label.display_rect = QRect(0, 0, 0, 0)

    def update_frame(self):
        if self.cap and not self.is_paused:
            ret, frame = self.cap.read()
            if ret:
                # 保存当前帧
                self.current_frame = frame
                
                # 转换颜色空间
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 创建QImage
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w
                qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
                
                # 计算缩放比例以保持原始宽高比
                label_size = self.video_label.size()
                video_ratio = w / h
                label_ratio = label_size.width() / label_size.height()
                
                pixmap = QPixmap.fromImage(qt_image)
                
                # 根据比例缩放图像
                if video_ratio > label_ratio:
                    # 如果视频更宽，则按宽度缩放
                    scaled_pixmap = pixmap.scaledToWidth(label_size.width(), Qt.SmoothTransformation)
                else:
                    # 如果视频更高，则按高度缩放
                    scaled_pixmap = pixmap.scaledToHeight(label_size.height(), Qt.SmoothTransformation)
                
                # 显示原始视频帧
                self.video_label.setPixmap(scaled_pixmap)
                
                # 计算视频在标签中的实际位置
                pixmap_size = scaled_pixmap.size()
                x_offset = (label_size.width() - pixmap_size.width()) // 2
                y_offset = (label_size.height() - pixmap_size.height()) // 2
                
                display_rect = QRect(x_offset, y_offset, pixmap_size.width(), pixmap_size.height())
                self.video_label.set_display_rect(display_rect)
                # 只在第一次设置裁剪框
                if not self.video_label.crop_rect or not self.video_label.crop_rect.isValid():
                    self.video_label.reset_crop_rect()
                self.video_label.update()
            else:
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)

    def toggle_pause(self):
        """暂停/播放切换"""
        self.is_paused = not self.is_paused

    def closeEvent(self, event):
        """窗口关闭时释放视频资源"""
        if self.cap:
            self.cap.release()
        event.accept()

    def save_cropped_video(self):
        if not self.cap or not self.video_label.crop_rect:
            return
        rect = self.video_label.crop_rect
        if rect.width() < MIN_CROP_SIZE or rect.height() < MIN_CROP_SIZE:
            self.show_message("裁剪区域太小！")
            return
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "保存裁剪视频",
            self.last_dir,
            "视频文件 (*.mp4 *.avi);;所有文件 (*)",
            options=options
        )
        if file_name:
            self.last_dir = os.path.dirname(file_name)
            self.settings.setValue('last_dir', self.last_dir)
            if not self.check_ffmpeg():
                self.show_message("未检测到ffmpeg，无法合成音频。请先安装ffmpeg并配置环境变量。")
                return
            progress = None
            try:
                # 1. 保存无声临时视频
                tmp_fd, tmp_video_path = tempfile.mkstemp(suffix='.mp4')
                os.close(tmp_fd)
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                fps = self.cap.get(cv2.CAP_PROP_FPS)
                video_w = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                video_h = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                pixmap = self.video_label.pixmap()
                if not pixmap:
                    self.show_message("无法获取视频帧！")
                    return
                label_size = self.video_label.size()
                pixmap_size = pixmap.size()
                x_offset = (label_size.width() - pixmap_size.width()) // 2
                y_offset = (label_size.height() - pixmap_size.height()) // 2
                
                # 以display_rect为基准，限定裁剪框在pixmap区域内
                display_rect = self.video_label.display_rect
                rect = self.video_label.crop_rect

                # 只允许在pixmap区域内裁剪
                crop_x = int(rect.x() * (video_w / display_rect.width()))
                crop_y = int(rect.y() * (video_h / display_rect.height()))
                crop_w = int(rect.width() * (video_w / display_rect.width()))
                crop_h = int(rect.height() * (video_h / display_rect.height()))

                # 保证裁剪区域不超出原视频边界
                crop_x = max(0, crop_x)
                crop_y = max(0, crop_y)
                crop_w = min(video_w - crop_x, crop_w)
                crop_h = min(video_h - crop_y, crop_h)
                total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
                progress = QProgressDialog("正在保存裁剪视频...", None, 0, total_frames, self)
                progress.setWindowTitle("请稍候")
                progress.setWindowModality(Qt.WindowModal)
                progress.setMinimumDuration(0)
                progress.setValue(0)
                out = cv2.VideoWriter(tmp_video_path, fourcc, fps, (crop_w, crop_h))
                frame_idx = 0
                while True:
                    ret, frame = self.cap.read()
                    if not ret:
                        break
                    cropped_frame = frame[crop_y:crop_y+crop_h, crop_x:crop_x+crop_w]
                    out.write(cropped_frame)
                    frame_idx += 1
                    if progress:
                        progress.setValue(frame_idx)
                        progress.setLabelText(f"正在保存裁剪视频...（{frame_idx}/{total_frames}）")
                        QApplication.processEvents()
                        if progress.wasCanceled():
                            out.release()
                            os.remove(tmp_video_path)
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            self.show_message("操作已取消")
                            return
                out.release()
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                if progress:
                    progress.setValue(total_frames)
                    progress.setLabelText("正在合成音频...")
                    QApplication.processEvents()
                # 2. 用ffmpeg合成音频
                ffmpeg_cmd = [
                    'ffmpeg', '-y',
                    '-i', self.video_path,
                    '-filter:v', f'crop={crop_w}:{crop_h}:{crop_x}:{crop_y}',
                    '-c:v', 'libx264',
                    '-crf', '18',
                    '-pix_fmt', 'yuv420p',
                    '-c:a', 'aac',
                    file_name
                ]
                result = subprocess.run(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if result.returncode != 0:
                    if progress:
                        progress.close()
                    self.show_message(f"ffmpeg合成音频失败：{result.stderr.decode('utf-8')}")
                    os.remove(tmp_video_path)
                    return
                os.remove(tmp_video_path)
                if progress:
                    progress.close()
                self.show_message("保存成功！")
                # 自动打开保存文件所在的文件夹
                folder = os.path.dirname(file_name)
                if platform.system() == 'Windows':
                    os.startfile(folder)
                elif platform.system() == 'Darwin':
                    subprocess.Popen(['open', folder])
                else:
                    subprocess.Popen(['xdg-open', folder])
            except Exception as e:
                if progress:
                    progress.close()
                self.show_message(f"保存失败: {e}")

    def check_ffmpeg(self):
        """检测ffmpeg命令是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return result.returncode == 0
        except Exception:
            return False

    def show_message(self, msg):
        """弹窗提示信息"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "提示", msg)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = VideoCropper()
    # 居中显示主窗口
    qr = window.frameGeometry()
    cp = QApplication.desktop().screen().rect().center()
    qr.moveCenter(cp)
    window.move(qr.topLeft())
    window.show()
    sys.exit(app.exec_())
